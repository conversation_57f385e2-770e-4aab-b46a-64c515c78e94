#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Python PyQt6 GUI 示例程序
适合初学者的Hello World程序
使用现代化的PyQt6框架开发
"""

import sys
import datetime
from PyQt6.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout,
                             QHBoxLayout, QGridLayout, QLabel, QLineEdit,
                             QComboBox, QPushButton, QTextEdit, QMessageBox,
                             QFrame, QSizePolicy)
from PyQt6.QtCore import Qt, pyqtSignal
from PyQt6.QtGui import QFont, QIcon


class HelloWorldApp(QMainWindow):
    """主应用程序类 - 基于PyQt6的现代化GUI应用"""

    # 自定义信号
    greeting_requested = pyqtSignal(str, str)

    def __init__(self):
        super().__init__()
        self.init_ui()
        self.init_signals()

    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle("🐍 Python PyQt6 GUI Hello World")
        self.setGeometry(100, 100, 600, 500)
        self.setMinimumSize(500, 400)

        # 设置窗口居中
        self.center_window()

        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # 创建主布局
        self.create_main_layout(central_widget)

        # 应用样式
        self.apply_styles()

    def center_window(self):
        """将窗口居中显示"""
        screen = QApplication.primaryScreen().geometry()
        window = self.geometry()
        x = (screen.width() - window.width()) // 2
        y = (screen.height() - window.height()) // 2
        self.move(x, y)

    def create_main_layout(self, parent):
        """创建主要布局和控件"""
        # 主垂直布局
        main_layout = QVBoxLayout(parent)
        main_layout.setSpacing(20)
        main_layout.setContentsMargins(30, 30, 30, 30)

        # 标题区域
        self.create_title_section(main_layout)

        # 输入区域
        self.create_input_section(main_layout)

        # 按钮区域
        self.create_button_section(main_layout)

        # 结果显示区域
        self.create_result_section(main_layout)

    def create_title_section(self, parent_layout):
        """创建标题区域"""
        title_label = QLabel("🐍 Python PyQt6 GUI 程序")
        title_font = QFont("Arial", 18, QFont.Weight.Bold)
        title_label.setFont(title_font)
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                color: #2c3e50;
                padding: 10px;
                background-color: #ecf0f1;
                border-radius: 8px;
                margin-bottom: 10px;
            }
        """)
        parent_layout.addWidget(title_label)

    def create_input_section(self, parent_layout):
        """创建输入区域"""
        # 输入区域框架
        input_frame = QFrame()
        input_frame.setFrameStyle(QFrame.Shape.StyledPanel)
        input_layout = QGridLayout(input_frame)
        input_layout.setSpacing(15)

        # 姓名输入
        name_label = QLabel("请输入您的姓名:")
        name_label.setFont(QFont("Arial", 10))
        self.name_input = QLineEdit()
        self.name_input.setPlaceholderText("请输入您的姓名...")
        self.name_input.setMinimumHeight(30)

        input_layout.addWidget(name_label, 0, 0)
        input_layout.addWidget(self.name_input, 0, 1)

        # 问候语选择
        greeting_label = QLabel("选择问候语:")
        greeting_label.setFont(QFont("Arial", 10))
        self.greeting_combo = QComboBox()
        self.greeting_combo.addItems(["你好", "Hello", "Bonjour", "Hola", "こんにちは"])
        self.greeting_combo.setMinimumHeight(30)

        input_layout.addWidget(greeting_label, 1, 0)
        input_layout.addWidget(self.greeting_combo, 1, 1)

        # 设置列拉伸
        input_layout.setColumnStretch(1, 1)

        parent_layout.addWidget(input_frame)

    def create_button_section(self, parent_layout):
        """创建按钮区域"""
        button_layout = QHBoxLayout()
        button_layout.setSpacing(10)

        # 问候按钮
        self.greet_btn = QPushButton("🎉 问候")
        self.greet_btn.setMinimumHeight(35)
        self.greet_btn.clicked.connect(self.show_greeting)

        # 时间按钮
        self.time_btn = QPushButton("⏰ 显示时间")
        self.time_btn.setMinimumHeight(35)
        self.time_btn.clicked.connect(self.show_time)

        # 清除按钮
        self.clear_btn = QPushButton("🗑️ 清除")
        self.clear_btn.setMinimumHeight(35)
        self.clear_btn.clicked.connect(self.clear_text)

        # 退出按钮
        self.exit_btn = QPushButton("❌ 退出")
        self.exit_btn.setMinimumHeight(35)
        self.exit_btn.clicked.connect(self.close)

        # 添加按钮到布局
        button_layout.addWidget(self.greet_btn)
        button_layout.addWidget(self.time_btn)
        button_layout.addWidget(self.clear_btn)
        button_layout.addWidget(self.exit_btn)

        parent_layout.addLayout(button_layout)

    def create_result_section(self, parent_layout):
        """创建结果显示区域"""
        result_label = QLabel("输出结果:")
        result_label.setFont(QFont("Arial", 10, QFont.Weight.Bold))
        parent_layout.addWidget(result_label)

        # 结果文本框
        self.result_text = QTextEdit()
        self.result_text.setMinimumHeight(150)
        self.result_text.setFont(QFont("Consolas", 10))
        self.result_text.setPlaceholderText("程序输出将显示在这里...")

        # 设置文本框可扩展
        self.result_text.setSizePolicy(QSizePolicy.Policy.Expanding,
                                       QSizePolicy.Policy.Expanding)

        parent_layout.addWidget(self.result_text)

    def apply_styles(self):
        """应用样式表"""
        self.setStyleSheet("""
            QMainWindow {
                background-color: #f8f9fa;
            }

            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 8px 16px;
                font-weight: bold;
                font-size: 10pt;
            }

            QPushButton:hover {
                background-color: #2980b9;
            }

            QPushButton:pressed {
                background-color: #21618c;
            }

            QLineEdit, QComboBox {
                border: 2px solid #bdc3c7;
                border-radius: 6px;
                padding: 5px;
                font-size: 10pt;
            }

            QLineEdit:focus, QComboBox:focus {
                border-color: #3498db;
            }

            QTextEdit {
                border: 2px solid #bdc3c7;
                border-radius: 6px;
                padding: 8px;
                background-color: white;
            }

            QFrame {
                background-color: white;
                border-radius: 8px;
                padding: 10px;
            }

            QLabel {
                color: #2c3e50;
            }
        """)

    def init_signals(self):
        """初始化信号槽连接"""
        # 连接回车键到问候功能
        self.name_input.returnPressed.connect(self.show_greeting)

        # 连接自定义信号
        self.greeting_requested.connect(self.handle_greeting_request)

    def show_greeting(self):
        """显示问候信息"""
        name = self.name_input.text().strip()
        if not name:
            QMessageBox.warning(self, "提示", "请先输入您的姓名！")
            self.name_input.setFocus()
            return

        greeting = self.greeting_combo.currentText()

        # 发射自定义信号
        self.greeting_requested.emit(greeting, name)

    def handle_greeting_request(self, greeting, name):
        """处理问候请求信号"""
        message = f"{greeting}, {name}! 欢迎使用Python PyQt6 GUI程序！\n"
        self.append_text(message)

    def show_time(self):
        """显示当前时间"""
        current_time = datetime.datetime.now()
        time_str = current_time.strftime("%Y年%m月%d日 %H:%M:%S")
        message = f"当前时间: {time_str}\n"
        self.append_text(message)

    def clear_text(self):
        """清除文本内容"""
        self.result_text.clear()

    def append_text(self, text):
        """向文本框追加内容"""
        self.result_text.append(text.rstrip())
        # 滚动到底部
        cursor = self.result_text.textCursor()
        cursor.movePosition(cursor.MoveOperation.End)
        self.result_text.setTextCursor(cursor)


def main():
    """主函数"""
    # 创建应用程序实例
    app = QApplication(sys.argv)

    # 设置应用程序信息
    app.setApplicationName("Python PyQt6 Hello World")
    app.setApplicationVersion("1.0.0")
    app.setOrganizationName("Python学习示例")

    # 创建主窗口
    window = HelloWorldApp()
    window.show()

    # 启动事件循环
    sys.exit(app.exec())


if __name__ == "__main__":
    main()
