#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PyQt6 Hello World 应用启动器
提供依赖检查和应用启动功能
"""

import sys
import subprocess
import importlib.util

def check_python_version():
    """检查Python版本"""
    if sys.version_info < (3, 8):
        print("❌ 错误: 需要Python 3.8或更高版本")
        print(f"   当前版本: {sys.version}")
        return False
    print(f"✅ Python版本检查通过: {sys.version.split()[0]}")
    return True

def check_pyqt6():
    """检查PyQt6是否已安装"""
    try:
        import PyQt6
        print(f"✅ PyQt6已安装: {PyQt6.QtCore.PYQT_VERSION_STR}")
        return True
    except ImportError:
        print("❌ PyQt6未安装")
        return False

def install_pyqt6():
    """安装PyQt6"""
    print("🔄 正在安装PyQt6...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "PyQt6"])
        print("✅ PyQt6安装成功")
        return True
    except subprocess.CalledProcessError:
        print("❌ PyQt6安装失败")
        return False

def run_application():
    """运行主应用程序"""
    try:
        print("🚀 启动PyQt6 Hello World应用...")
        from main import main
        main()
    except Exception as e:
        print(f"❌ 应用启动失败: {e}")
        return False
    return True

def main():
    """主函数"""
    print("=" * 50)
    print("🐍 PyQt6 Hello World 应用启动器")
    print("=" * 50)
    
    # 检查Python版本
    if not check_python_version():
        input("按回车键退出...")
        sys.exit(1)
    
    # 检查PyQt6
    if not check_pyqt6():
        choice = input("是否自动安装PyQt6? (y/n): ").lower().strip()
        if choice in ['y', 'yes', '是']:
            if not install_pyqt6():
                input("按回车键退出...")
                sys.exit(1)
        else:
            print("请手动安装PyQt6: pip install PyQt6")
            input("按回车键退出...")
            sys.exit(1)
    
    # 运行应用
    print("-" * 50)
    if run_application():
        print("✅ 应用正常退出")
    else:
        print("❌ 应用异常退出")
        input("按回车键退出...")
        sys.exit(1)

if __name__ == "__main__":
    main()
