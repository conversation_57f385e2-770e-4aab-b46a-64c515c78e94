#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Python Tkinter GUI 示例程序
适合初学者的Hello World程序
"""

import tkinter as tk
from tkinter import ttk, messagebox
import datetime


class HelloWorldApp:
    """主应用程序类"""
    
    def __init__(self, root):
        self.root = root
        self.setup_window()
        self.create_widgets()
        
    def setup_window(self):
        """设置主窗口"""
        self.root.title("Python GUI Hello World")
        self.root.geometry("500x400")
        self.root.resizable(True, True)
        
        # 设置窗口居中
        self.center_window()
        
    def center_window(self):
        """将窗口居中显示"""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f'{width}x{height}+{x}+{y}')
        
    def create_widgets(self):
        """创建界面控件"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="20")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        
        # 标题标签
        title_label = ttk.Label(
            main_frame, 
            text="🐍 Python GUI 程序", 
            font=("Arial", 16, "bold")
        )
        title_label.grid(row=0, column=0, columnspan=2, pady=(0, 20))
        
        # 用户名输入
        ttk.Label(main_frame, text="请输入您的姓名:").grid(
            row=1, column=0, sticky=tk.W, pady=5
        )
        self.name_var = tk.StringVar()
        name_entry = ttk.Entry(main_frame, textvariable=self.name_var, width=30)
        name_entry.grid(row=1, column=1, sticky=(tk.W, tk.E), pady=5)
        
        # 问候语选择
        ttk.Label(main_frame, text="选择问候语:").grid(
            row=2, column=0, sticky=tk.W, pady=5
        )
        self.greeting_var = tk.StringVar(value="你好")
        greeting_combo = ttk.Combobox(
            main_frame, 
            textvariable=self.greeting_var,
            values=["你好", "Hello", "Bonjour", "Hola", "こんにちは"],
            state="readonly",
            width=27
        )
        greeting_combo.grid(row=2, column=1, sticky=(tk.W, tk.E), pady=5)
        
        # 按钮框架
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=3, column=0, columnspan=2, pady=20)
        
        # 问候按钮
        greet_btn = ttk.Button(
            button_frame, 
            text="🎉 问候", 
            command=self.show_greeting
        )
        greet_btn.pack(side=tk.LEFT, padx=5)
        
        # 时间按钮
        time_btn = ttk.Button(
            button_frame, 
            text="⏰ 显示时间", 
            command=self.show_time
        )
        time_btn.pack(side=tk.LEFT, padx=5)
        
        # 清除按钮
        clear_btn = ttk.Button(
            button_frame, 
            text="🗑️ 清除", 
            command=self.clear_text
        )
        clear_btn.pack(side=tk.LEFT, padx=5)
        
        # 退出按钮
        exit_btn = ttk.Button(
            button_frame, 
            text="❌ 退出", 
            command=self.root.quit
        )
        exit_btn.pack(side=tk.LEFT, padx=5)
        
        # 结果显示区域
        ttk.Label(main_frame, text="输出结果:").grid(
            row=4, column=0, sticky=(tk.W, tk.N), pady=(10, 5)
        )
        
        # 文本框和滚动条
        text_frame = ttk.Frame(main_frame)
        text_frame.grid(row=4, column=1, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(10, 0))
        text_frame.columnconfigure(0, weight=1)
        text_frame.rowconfigure(0, weight=1)
        
        self.result_text = tk.Text(
            text_frame, 
            height=8, 
            width=40,
            wrap=tk.WORD,
            font=("Consolas", 10)
        )
        scrollbar = ttk.Scrollbar(text_frame, orient=tk.VERTICAL, command=self.result_text.yview)
        self.result_text.configure(yscrollcommand=scrollbar.set)
        
        self.result_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        
        # 配置主框架的行权重
        main_frame.rowconfigure(4, weight=1)
        
        # 绑定回车键
        name_entry.bind('<Return>', lambda e: self.show_greeting())
        
    def show_greeting(self):
        """显示问候信息"""
        name = self.name_var.get().strip()
        if not name:
            messagebox.showwarning("提示", "请先输入您的姓名！")
            return
            
        greeting = self.greeting_var.get()
        message = f"{greeting}, {name}! 欢迎使用Python GUI程序！\n"
        
        self.append_text(message)
        
    def show_time(self):
        """显示当前时间"""
        current_time = datetime.datetime.now()
        time_str = current_time.strftime("%Y年%m月%d日 %H:%M:%S")
        message = f"当前时间: {time_str}\n"
        
        self.append_text(message)
        
    def clear_text(self):
        """清除文本内容"""
        self.result_text.delete(1.0, tk.END)
        
    def append_text(self, text):
        """向文本框追加内容"""
        self.result_text.insert(tk.END, text)
        self.result_text.see(tk.END)  # 滚动到底部


def main():
    """主函数"""
    # 创建主窗口
    root = tk.Tk()
    
    # 创建应用程序实例
    app = HelloWorldApp(root)
    
    # 启动事件循环
    root.mainloop()


if __name__ == "__main__":
    main()
